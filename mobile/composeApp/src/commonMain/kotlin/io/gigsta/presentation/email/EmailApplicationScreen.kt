package io.gigsta.presentation.email

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.gigsta.domain.model.EmailGenerationStep
import io.gigsta.presentation.email.components.*
import io.gigsta.presentation.theme.Spacing

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailApplicationScreen(
    onNavigateBack: () -> Unit,
    viewModel: EmailApplicationViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )
    var showBottomSheet by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Buat Email Lamaran",
                        fontWeight = FontWeight.SemiBold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Kembali"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        },
        floatingActionButton = {
            if (uiState.emailApplication == null && !uiState.isGenerating) {
                FloatingActionButton(
                    onClick = { showBottomSheet = true },
                    containerColor = MaterialTheme.colorScheme.primary
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Buat Email"
                    )
                }
            } else if (uiState.emailApplication != null) {
                FloatingActionButton(
                    onClick = { showBottomSheet = true },
                    containerColor = MaterialTheme.colorScheme.secondary
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit Form"
                    )
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Main content - Email result or empty state
            if (uiState.emailApplication != null || uiState.isGenerating) {
                EmailResultSection(
                    emailApplication = uiState.emailApplication,
                    isGenerating = uiState.isGenerating,
                    onRegenerateEmail = viewModel::regenerateEmail,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(Spacing.medium)
                )
            } else {
                // Empty state
                EmptyEmailState(
                    onCreateEmail = { showBottomSheet = true },
                    modifier = Modifier.fillMaxSize()
                )
            }
        }

        // Bottom Sheet for form input
        if (showBottomSheet) {
            ModalBottomSheet(
                onDismissRequest = { showBottomSheet = false },
                sheetState = bottomSheetState,
                modifier = Modifier.fillMaxHeight(0.9f)
            ) {
                EmailFormBottomSheet(
                    uiState = uiState,
                    viewModel = viewModel,
                    onDismiss = { showBottomSheet = false },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Spacing.medium)
                )
            }
        }
    }
}

@Composable
private fun EmptyEmailState(
    onCreateEmail: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(Spacing.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Add,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
        )

        Spacer(modifier = Modifier.height(Spacing.large))

        Text(
            text = "Belum Ada Email Lamaran",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(Spacing.small))

        Text(
            text = "Buat email lamaran profesional yang dipersonalisasi berdasarkan CV/resume dan informasi pekerjaan Anda",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(Spacing.extraLarge))

        Button(
            onClick = onCreateEmail,
            modifier = Modifier.fillMaxWidth(0.6f)
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(Spacing.small))
            Text("Buat Email Lamaran")
        }
    }
}

@Composable
private fun EmailFormBottomSheet(
    uiState: EmailApplicationUiState,
    viewModel: EmailApplicationViewModel,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val scrollState = rememberScrollState()

    Column(
        modifier = modifier
            .fillMaxWidth()
            .verticalScroll(scrollState),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Form Email Lamaran",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.SemiBold
            )

            TextButton(onClick = onDismiss) {
                Text("Tutup")
            }
        }

        Divider()

        // Error message
        uiState.error?.let { error ->
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                ),
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(Spacing.medium)
                )
            }
        }

        // Resume Upload Section
        ResumeUploadSection(
            isLoading = false,
            uploadSuccess = uiState.isResumeUploaded,
            existingResume = uiState.resumeInfo,
            error = if (uiState.error?.contains("resume") == true) uiState.error else null,
            onFileSelected = { fileData, fileName, mimeType ->
                viewModel.onResumeUploaded(fileData, fileName, mimeType)
            },
            onViewResume = viewModel::onViewResume,
            onDeleteResume = viewModel::onDeleteResume,
            showTitle = false
        )

        Divider()

        // Job Info Section
        JobInfoInputSection(
            inputMethod = uiState.inputMethod,
            jobDescription = uiState.jobDescription,
            onJobDescriptionChange = viewModel::onJobDescriptionChanged,
            onInputMethodChange = viewModel::onInputMethodChanged,
            onImageSelected = { imageData, fileName, mimeType ->
                viewModel.onJobImageSelected(imageData, fileName, mimeType)
            },
            hasJobImage = uiState.hasJobImage,
            error = if (uiState.error?.contains("lowongan") == true ||
                        uiState.error?.contains("gambar") == true ||
                        uiState.error?.contains("deskripsi") == true) uiState.error else null,
            showTitle = false
        )

        // Generate Button
        val canGenerate = uiState.isResumeUploaded && when (uiState.inputMethod) {
            io.gigsta.domain.model.InputMethod.TEXT -> uiState.jobDescription.isNotBlank()
            io.gigsta.domain.model.InputMethod.IMAGE -> uiState.hasJobImage
        }

        Button(
            onClick = {
                viewModel.generateEmail()
                onDismiss()
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = canGenerate && !uiState.isGenerating
        ) {
            if (uiState.isGenerating) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Spacer(modifier = Modifier.width(Spacing.small))
                Text("Membuat Email...")
            } else {
                Text("Buat Email Lamaran")
            }
        }

        // Bottom padding for better scrolling
        Spacer(modifier = Modifier.height(Spacing.large))
    }
}
